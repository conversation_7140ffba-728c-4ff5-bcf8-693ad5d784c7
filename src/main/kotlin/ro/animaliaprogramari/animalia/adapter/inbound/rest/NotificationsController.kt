package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ApiResponse
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.notification.*
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.port.inbound.NotificationManagementUseCase
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.notification.NotificationId

@RestController
@RequestMapping("/notifications")
@CrossOrigin(origins = ["http://localhost:3000", "http://localhost:8080"])
@Tag(name = "Notifications", description = "Operations for managing notifications")
class NotificationsController(
    private val notificationManagementUseCase: NotificationManagementUseCase,
) {
    private val logger = LoggerFactory.getLogger(NotificationsController::class.java)

    /**
     * POST /api/notifications/generate-test-data
     * Generate test notification data for testing purposes
     */
    @PostMapping("/generate-test-data")
    @Operation(summary = "Generate test notification data", description = "Creates test notifications for development and testing")
    fun generateTestData(
        @Parameter(description = "Number of test notifications to generate")
        @RequestParam(defaultValue = "10") count: Int
    ): ResponseEntity<ApiResponse<GenerateTestDataResponse>> {
        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(401).body(ApiResponse.error("User not authenticated"))

            logger.info("Generating $count test notifications for user: ${currentUser.userId.value}")

            val command = GenerateTestNotificationDataCommand(
                userId = currentUser.userId,
                salonId = currentUser.currentSalonId ?: currentUser.staffAssociations.firstOrNull()?.salonId
                    ?: return ResponseEntity.badRequest().body(ApiResponse.error("No salon associated with user")),
                count = count
            )

            val result = notificationManagementUseCase.generateTestData(command)

            ResponseEntity.ok(ApiResponse.success(result))
        } catch (e: Exception) {
            logger.error("Error generating test data", e)
            ResponseEntity.status(500).body(ApiResponse.error("Failed to generate test data: ${e.message}"))
        }
    }

    /**
     * GET /api/notifications/stats
     * Get notification statistics
     */
    @GetMapping("/stats")
    @Operation(summary = "Get notification statistics", description = "Returns comprehensive notification statistics")
    fun getNotificationStats(): ResponseEntity<ApiResponse<NotificationStatsResponse>> {
        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(401).body(ApiResponse.error("User not authenticated"))

            logger.info("Getting notification stats for user: ${currentUser.userId.value}")

            val query = GetNotificationStatsQuery(
                userId = currentUser.userId,
                salonId = currentUser.currentSalonId ?: currentUser.staffAssociations.firstOrNull()?.salonId
                    ?: return ResponseEntity.badRequest().body(ApiResponse.error("No salon associated with user"))
            )

            val stats = notificationManagementUseCase.getNotificationStats(query)

            ResponseEntity.ok(ApiResponse.success(stats))
        } catch (e: Exception) {
            logger.error("Error getting notification stats", e)
            ResponseEntity.status(500).body(ApiResponse.error("Failed to get notification stats: ${e.message}"))
        }
    }

    /**
     * POST /api/notifications/mark-read
     * Mark all notifications as read (bulk operation)
     */
    @PostMapping("/mark-read")
    @Operation(summary = "Mark all notifications as read", description = "Bulk operation to mark all user notifications as read")
    fun markAllAsRead(): ResponseEntity<ApiResponse<BulkMarkAsReadResponse>> {
        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(401).body(ApiResponse.error("User not authenticated"))

            logger.info("Marking all notifications as read for user: ${currentUser.userId.value}")

            val command = BulkMarkNotificationsAsReadCommand(
                userId = currentUser.userId,
                salonId = currentUser.currentSalonId ?: currentUser.staffAssociations.firstOrNull()?.salonId
                    ?: return ResponseEntity.badRequest().body(ApiResponse.error("No salon associated with user"))
            )

            val result = notificationManagementUseCase.markAllAsRead(command)

            ResponseEntity.ok(ApiResponse.success(result))
        } catch (e: Exception) {
            logger.error("Error marking notifications as read", e)
            ResponseEntity.status(500).body(ApiResponse.error("Failed to mark notifications as read: ${e.message}"))
        }
    }

    /**
     * POST /api/notifications/test
     * Send a test notification
     */
    @PostMapping("/test")
    @Operation(summary = "Send test notification", description = "Sends a test push notification to verify the system is working")
    fun sendTestNotification(
        @Valid @RequestBody request: SendTestNotificationRequest
    ): ResponseEntity<ApiResponse<SendTestNotificationResponse>> {
        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(401).body(ApiResponse.error("User not authenticated"))

            logger.info("Sending test notification for user: ${currentUser.userId.value}")

            val command = SendTestNotificationCommand(
                userId = currentUser.userId,
                salonId = currentUser.currentSalonId ?: currentUser.staffAssociations.firstOrNull()?.salonId
                    ?: return ResponseEntity.badRequest().body(ApiResponse.error("No salon associated with user")),
                title = request.title,
                message = request.message,
                notificationType = request.type
            )

            val result = notificationManagementUseCase.sendTestNotification(command)

            ResponseEntity.ok(ApiResponse.success(result))
        } catch (e: Exception) {
            logger.error("Error sending test notification", e)
            ResponseEntity.status(500).body(ApiResponse.error("Failed to send test notification: ${e.message}"))
        }
    }

    /**
     * GET /api/notifications
     * Get all notifications for the current user with pagination
     */
    @GetMapping
    @Operation(summary = "Get all notifications", description = "Returns paginated notifications for the current user")
    fun getAllNotifications(
        @Parameter(description = "Page number (0-based)")
        @RequestParam(defaultValue = "0") page: Int,
        @Parameter(description = "Number of notifications per page")
        @RequestParam(defaultValue = "20") pageSize: Int,
        @Parameter(description = "Return only unread notifications")
        @RequestParam(defaultValue = "false") unreadOnly: Boolean,
        @Parameter(description = "Filter by notification type")
        @RequestParam(required = false) type: String?
    ): ResponseEntity<ApiResponse<PaginatedNotificationsResponse>> {
        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(401).body(ApiResponse.error("User not authenticated"))

            logger.info("Getting notifications for user: ${currentUser.userId.value}, page: $page, pageSize: $pageSize, unreadOnly: $unreadOnly, type: $type")

            val query = GetUserNotificationsQuery(
                userId = currentUser.userId,
                salonId = currentUser.currentSalonId ?: currentUser.staffAssociations.firstOrNull()?.salonId
                    ?: return ResponseEntity.badRequest().body(ApiResponse.error("No salon associated with user")),
                page = page,
                pageSize = pageSize,
                unreadOnly = unreadOnly,
                type = type
            )

            val paginatedResponse = notificationManagementUseCase.getUserNotifications(query)

            ResponseEntity.ok(ApiResponse.success(paginatedResponse))
        } catch (e: Exception) {
            logger.error("Error getting notifications", e)
            ResponseEntity.status(500).body(ApiResponse.error("Failed to get notifications: ${e.message}"))
        }
    }

    /**
     * PUT /api/notifications/{id}/read
     * Mark a specific notification as read
     */
    @PutMapping("/{id}/read")
    @Operation(summary = "Mark notification as read", description = "Marks a specific notification as read")
    fun markNotificationAsRead(
        @Parameter(description = "Notification ID")
        @PathVariable id: String
    ): ResponseEntity<ApiResponse<NotificationResponse>> {
        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(401).body(ApiResponse.error("User not authenticated"))

            logger.info("Marking notification as read: $id for user: ${currentUser.userId.value}")

            val command = MarkNotificationAsReadCommand(
                notificationId = NotificationId.of(id),
                userId = currentUser.userId,
                salonId = currentUser.currentSalonId ?: currentUser.staffAssociations.firstOrNull()?.salonId
                    ?: return ResponseEntity.badRequest().body(ApiResponse.error("No salon associated with user"))
            )

            val result = notificationManagementUseCase.markNotificationAsRead(command)

            ResponseEntity.ok(ApiResponse.success(result))
        } catch (e: EntityNotFoundException) {
            logger.warn("Notification not found: $id")
            ResponseEntity.status(404).body(ApiResponse.error("Notification not found"))
        } catch (e: Exception) {
            logger.error("Error marking notification as read", e)
            ResponseEntity.status(500).body(ApiResponse.error("Failed to mark notification as read: ${e.message}"))
        }
    }
}
