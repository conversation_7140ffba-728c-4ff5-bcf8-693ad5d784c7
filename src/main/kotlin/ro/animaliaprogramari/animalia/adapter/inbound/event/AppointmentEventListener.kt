package ro.animaliaprogramari.animalia.adapter.inbound.event

import org.slf4j.LoggerFactory
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.domain.event.appointment.*
import ro.animaliaprogramari.animalia.domain.service.PersonalizedSmsService

/**
 * Listener that sends personalized SMS notifications for appointment related events.
 * Uses PersonalizedSmsService to create warm, professional messages in Romanian.
 */
@Component
class AppointmentEventListener(
    private val personalizedSmsService: PersonalizedSmsService,
) {
    private val logger = LoggerFactory.getLogger(AppointmentEventListener::class.java)

    @EventListener
    fun onAppointmentScheduled(event: AppointmentScheduledEvent) {
        logger.debug("Processing appointment scheduled event for client: ${event.clientId.value}")
        personalizedSmsService.sendAppointmentScheduledNotification(
            clientId = event.clientId,
            petId = event.petId,
            appointmentId = event.appointmentId,
            appointmentDate = event.appointmentDate,
            startTime = event.startTime,
        )
    }

    @EventListener
    fun onAppointmentCancelled(event: AppointmentCancelledEvent) {
        logger.debug("Processing appointment cancelled event for client: ${event.clientId.value}")
        personalizedSmsService.sendAppointmentCancelledNotification(
            clientId = event.clientId,
            petId = null, // Not available in this event
            appointmentId = event.appointmentId,
            appointmentDate = event.appointmentDate,
        )
    }

    @EventListener
    fun onAppointmentRescheduled(event: AppointmentRescheduledEvent) {
        logger.debug("Processing appointment rescheduled event for client: ${event.clientId.value}")
        personalizedSmsService.sendAppointmentRescheduledNotification(
            clientId = event.clientId,
            petId = null, // Not available in this event
            appointmentId = event.appointmentId,
            oldDate = event.oldDate,
            newDate = event.newDate,
            newStartTime = event.newStartTime,
        )
    }

    @EventListener
    fun onAppointmentDeleted(event: AppointmentDeletedEvent) {
        logger.debug("Processing appointment deleted event for client: ${event.clientId.value}")
        personalizedSmsService.sendAppointmentDeletedNotification(
            clientId = event.clientId,
            petId = null, // Not available in this event
            appointmentId = event.appointmentId,
            appointmentDate = event.appointmentDate,
        )
    }

    @EventListener
    fun onAppointmentReminder(event: AppointmentReminderEvent) {
        logger.debug("Processing appointment reminder event for client: ${event.clientId.value}")
        personalizedSmsService.sendAppointmentReminderNotification(
            clientId = event.clientId,
            petId = null, // Not available in this event
            appointmentId = event.appointmentId,
            appointmentDate = event.appointmentDate,
            startTime = null, // Not available in this event
            reminderType = event.reminderType,
        )
    }
}
