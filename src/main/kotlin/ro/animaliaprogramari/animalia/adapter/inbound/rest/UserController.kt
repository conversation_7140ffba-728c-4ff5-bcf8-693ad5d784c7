package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ApiResponse
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.notification.*
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.port.inbound.NotificationManagementUseCase
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.domain.model.SalonId

@RestController
@RequestMapping("/users")
@CrossOrigin(origins = ["http://localhost:3000", "http://localhost:8080"])
@Tag(name = "Users", description = "Operations for user management")
class UserController(
    private val notificationManagementUseCase: NotificationManagementUseCase,
) {
    private val logger = LoggerFactory.getLogger(UserController::class.java)

    /**
     * POST /api/users/fcm-token
     * Register FCM token for push notifications
     */
    @PostMapping("/fcm-token")
    @Operation(summary = "Register FCM token", description = "Register FCM token for push notifications")
    fun registerFcmToken(
        @Valid @RequestBody request: RegisterFcmTokenRequest
    ): ResponseEntity<ApiResponse<FcmTokenResponse>> {
        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(401).body(ApiResponse.error("User not authenticated"))

            logger.info("Registering FCM token for user: ${currentUser.userId.value}")

            val command = RegisterFcmTokenCommand(
                userId = currentUser.userId,
                salonId = SalonId.of(request.salonId),
                token = request.token,
                deviceId = request.deviceId,
                deviceType = request.deviceType
            )

            val result = notificationManagementUseCase.registerFcmToken(command)
            
            ResponseEntity.ok(ApiResponse.success(result))
        } catch (e: Exception) {
            logger.error("Error registering FCM token", e)
            ResponseEntity.status(500).body(ApiResponse.error("Failed to register FCM token: ${e.message}"))
        }
    }

    /**
     * PUT /api/users/fcm-token
     * Update existing FCM token
     */
    @PutMapping("/fcm-token")
    @Operation(summary = "Update FCM token", description = "Update existing FCM token")
    fun updateFcmToken(
        @Valid @RequestBody request: UpdateFcmTokenRequest,
        @RequestParam oldToken: String
    ): ResponseEntity<ApiResponse<FcmTokenResponse>> {
        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(401).body(ApiResponse.error("User not authenticated"))

            logger.info("Updating FCM token for user: ${currentUser.userId.value}")

            val command = UpdateFcmTokenCommand(
                userId = currentUser.userId,
                salonId = currentUser.currentSalonId ?: currentUser.staffAssociations.firstOrNull()?.salonId
                    ?: return ResponseEntity.badRequest().body(ApiResponse.error("No salon associated with user")),
                oldToken = oldToken,
                newToken = request.newToken
            )

            val result = notificationManagementUseCase.updateFcmToken(command)
            
            ResponseEntity.ok(ApiResponse.success(result))
        } catch (e: Exception) {
            logger.error("Error updating FCM token", e)
            ResponseEntity.status(500).body(ApiResponse.error("Failed to update FCM token: ${e.message}"))
        }
    }

    /**
     * DELETE /api/users/fcm-token
     * Deactivate FCM token
     */
    @DeleteMapping("/fcm-token")
    @Operation(summary = "Deactivate FCM token", description = "Deactivate FCM token")
    fun deactivateFcmToken(
        @RequestParam token: String
    ): ResponseEntity<ApiResponse<Map<String, Any>>> {
        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(401).body(ApiResponse.error("User not authenticated"))

            logger.info("Deactivating FCM token for user: ${currentUser.userId.value}")

            val command = DeactivateFcmTokenCommand(
                userId = currentUser.userId,
                salonId = currentUser.currentSalonId ?: currentUser.staffAssociations.firstOrNull()?.salonId
                    ?: return ResponseEntity.badRequest().body(ApiResponse.error("No salon associated with user")),
                token = token
            )

            val success = notificationManagementUseCase.deactivateFcmToken(command)
            
            val result = mapOf(
                "success" to success,
                "message" to if (success) "FCM token deactivated successfully" else "FCM token not found"
            )
            
            ResponseEntity.ok(ApiResponse.success(result))
        } catch (e: Exception) {
            logger.error("Error deactivating FCM token", e)
            ResponseEntity.status(500).body(ApiResponse.error("Failed to deactivate FCM token: ${e.message}"))
        }
    }
}
