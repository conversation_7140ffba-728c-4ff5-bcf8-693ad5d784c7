package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.Notification
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationStatus
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationType
import java.time.LocalDateTime

/**
 * Spring Data JPA repository for notifications
 */
@Repository
interface SpringNotificationRepository : JpaRepository<Notification, String> {
    /**
     * Find notifications by salon ID with pagination
     */
    @Query("SELECT n FROM Notification n WHERE n.salonId = :salonId ORDER BY n.createdAt DESC")
    fun findBySalonId(@Param("salonId") salonId: String, pageable: Pageable): List<Notification>

    /**
     * Find notifications by user ID (for push notifications) with pagination
     */
    @Query("SELECT n FROM Notification n WHERE n.recipientType = 'USER' AND n.recipientValue = :userId ORDER BY n.createdAt DESC")
    fun findByUserId(@Param("userId") userId: String, pageable: Pageable): List<Notification>

    /**
     * Find notifications by appointment ID
     */
    fun findByAppointmentId(appointmentId: String): List<Notification>

    /**
     * Find notifications by status
     */
    @Query("SELECT n FROM Notification n WHERE n.status = :status ORDER BY n.createdAt DESC")
    fun findByStatus(@Param("status") status: NotificationStatus, pageable: Pageable): List<Notification>

    /**
     * Find notifications by type and salon
     */
    fun findByTypeAndSalonId(type: NotificationType, salonId: String): List<Notification>

    /**
     * Count notifications by status and salon
     */
    fun countByStatusAndSalonId(status: NotificationStatus, salonId: String): Long

    /**
     * Count notifications by type and salon
     */
    fun countByTypeAndSalonId(type: NotificationType, salonId: String): Long

    /**
     * Count notifications sent today for a salon
     */
    @Query("SELECT COUNT(n) FROM Notification n WHERE n.salonId = :salonId AND n.createdAt >= :startOfDay AND n.createdAt < :endOfDay")
    fun countTodayBySalonId(@Param("salonId") salonId: String, @Param("startOfDay") startOfDay: LocalDateTime, @Param("endOfDay") endOfDay: LocalDateTime): Long

    /**
     * Count notifications sent this week for a salon
     */
    @Query("SELECT COUNT(n) FROM Notification n WHERE n.salonId = :salonId AND n.createdAt >= :weekStart")
    fun countThisWeekBySalonId(@Param("salonId") salonId: String, @Param("weekStart") weekStart: LocalDateTime): Long

    /**
     * Count notifications sent this month for a salon
     */
    @Query("SELECT COUNT(n) FROM Notification n WHERE n.salonId = :salonId AND n.createdAt >= :monthStart")
    fun countThisMonthBySalonId(@Param("salonId") salonId: String, @Param("monthStart") monthStart: LocalDateTime): Long

    /**
     * Get notifications for average delivery time calculation
     */
    @Query("""
        SELECT n FROM Notification n
        WHERE n.salonId = :salonId
        AND n.status = 'DELIVERED'
        AND n.sentAt IS NOT NULL
        AND n.deliveredAt IS NOT NULL
    """)
    fun findDeliveredNotificationsBySalonId(@Param("salonId") salonId: String): List<Notification>

    /**
     * Find notifications created between dates
     */
    fun findByCreatedAtBetween(startDate: LocalDateTime, endDate: LocalDateTime): List<Notification>

    /**
     * Delete old notifications (cleanup)
     */
    @Modifying
    @Query("DELETE FROM Notification n WHERE n.createdAt < :cutoffDate")
    fun deleteOlderThan(@Param("cutoffDate") cutoffDate: LocalDateTime): Int

    /**
     * Mark notifications as read for a user
     */
    @Modifying
    @Query("UPDATE Notification n SET n.isRead = true, n.updatedAt = :now WHERE n.recipientType = 'USER' AND n.recipientValue = :userId AND n.salonId = :salonId AND n.isRead = false")
    fun markAsReadByUserId(@Param("userId") userId: String, @Param("salonId") salonId: String, @Param("now") now: LocalDateTime = LocalDateTime.now()): Int
}
