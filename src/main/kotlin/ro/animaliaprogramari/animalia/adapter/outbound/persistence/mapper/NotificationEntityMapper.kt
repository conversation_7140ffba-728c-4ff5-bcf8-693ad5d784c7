package ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper

import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.Notification as NotificationEntity
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationType as EntityNotificationType
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationStatus as EntityNotificationStatus
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.RecipientType
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.ContentType
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.model.notification.*
import ro.animaliaprogramari.animalia.domain.model.notification.NotificationStatus as DomainNotificationStatus
import ro.animaliaprogramari.animalia.domain.model.notification.SmsNotificationType as DomainNotificationType

/**
 * Mapper between Notification domain model and Notification JPA entity
 * This handles the translation between the pure domain model and the persistence layer
 */
@Component
class NotificationEntityMapper(
    private val objectMapper: ObjectMapper
) {
    /**
     * Convert domain model to JPA entity
     */
    fun toEntity(domain: Notification): NotificationEntity {
        val (recipientType, recipientValue) = when (domain.recipient) {
            is NotificationRecipient.Phone -> RecipientType.PHONE to domain.recipient.phoneNumber.value
            is NotificationRecipient.User -> RecipientType.USER to domain.recipient.userId.value
            is NotificationRecipient.Email -> RecipientType.EMAIL to domain.recipient.email.value
        }

        val contentType: ContentType
        val title: String?
        val body: String
        val data: String?

        when (domain.content) {
            is NotificationContent.Text -> {
                contentType = ContentType.TEXT
                title = null
                body = domain.content.message
                data = null
            }
            is NotificationContent.Push -> {
                contentType = ContentType.PUSH
                title = domain.content.title
                body = domain.content.body
                data = if (domain.content.data.isNotEmpty()) objectMapper.writeValueAsString(domain.content.data) else null
            }
            is NotificationContent.Email -> {
                contentType = ContentType.EMAIL
                title = domain.content.subject
                body = domain.content.body
                data = null
            }
        }

        return NotificationEntity(
            id = domain.id.value,
            type = mapDomainTypeToEntity(domain.type),
            recipientType = recipientType,
            recipientValue = recipientValue,
            contentType = contentType,
            title = title,
            body = body,
            data = data,
            status = mapDomainStatusToEntity(domain.status),
            appointmentId = domain.appointmentId?.value,
            salonId = domain.salonId.value,
            sentAt = domain.sentAt,
            deliveredAt = domain.deliveredAt,
            failureReason = domain.failureReason,
            retryCount = domain.retryCount,
            maxRetries = domain.maxRetries,
            isRead = false, // Default to false, can be updated later
            createdAt = domain.createdAt,
            updatedAt = domain.updatedAt,
        )
    }

    /**
     * Convert JPA entity to domain model
     */
    fun toDomain(entity: NotificationEntity): Notification {
        val recipient = when (entity.recipientType) {
            RecipientType.PHONE -> NotificationRecipient.Phone(PhoneNumber.of(entity.recipientValue))
            RecipientType.USER -> NotificationRecipient.User(UserId.of(entity.recipientValue))
            RecipientType.EMAIL -> NotificationRecipient.Email(Email.of(entity.recipientValue))
        }

        val content = when (entity.contentType) {
            ContentType.TEXT -> NotificationContent.Text(entity.body)
            ContentType.PUSH -> {
                val dataMap = entity.data?.let {
                    objectMapper.readValue(it, Map::class.java) as Map<String, String>
                } ?: emptyMap()
                NotificationContent.Push(
                    title = entity.title ?: "",
                    body = entity.body,
                    data = dataMap
                )
            }
            ContentType.EMAIL -> NotificationContent.Email(
                subject = entity.title ?: "",
                body = entity.body,
                isHtml = false
            )
        }

        return Notification(
            id = NotificationId.of(entity.id),
            type = mapEntityTypeToDomain(entity.type),
            recipient = recipient,
            content = content,
            status = mapEntityStatusToDomain(entity.status),
            appointmentId = entity.appointmentId?.let { AppointmentId.of(it) },
            salonId = SalonId.of(entity.salonId),
            sentAt = entity.sentAt,
            deliveredAt = entity.deliveredAt,
            failureReason = entity.failureReason,
            retryCount = entity.retryCount,
            maxRetries = entity.maxRetries,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt,
        )
    }

    private fun mapDomainTypeToEntity(domainType: DomainNotificationType): EntityNotificationType {
        return when (domainType) {
            DomainNotificationType.SMS -> EntityNotificationType.SMS
            DomainNotificationType.PUSH -> EntityNotificationType.PUSH
            DomainNotificationType.EMAIL -> EntityNotificationType.EMAIL
        }
    }

    private fun mapEntityTypeToDomain(entityType: EntityNotificationType): DomainNotificationType {
        return when (entityType) {
            EntityNotificationType.SMS -> DomainNotificationType.SMS
            EntityNotificationType.PUSH -> DomainNotificationType.PUSH
            EntityNotificationType.EMAIL -> DomainNotificationType.EMAIL
        }
    }

    private fun mapDomainStatusToEntity(domainStatus: DomainNotificationStatus): EntityNotificationStatus {
        return when (domainStatus) {
            DomainNotificationStatus.PENDING -> EntityNotificationStatus.PENDING
            DomainNotificationStatus.SENT -> EntityNotificationStatus.SENT
            DomainNotificationStatus.DELIVERED -> EntityNotificationStatus.DELIVERED
            DomainNotificationStatus.FAILED -> EntityNotificationStatus.FAILED
            DomainNotificationStatus.CANCELLED -> EntityNotificationStatus.CANCELLED
        }
    }

    private fun mapEntityStatusToDomain(entityStatus: EntityNotificationStatus): DomainNotificationStatus {
        return when (entityStatus) {
            EntityNotificationStatus.PENDING -> DomainNotificationStatus.PENDING
            EntityNotificationStatus.SENT -> DomainNotificationStatus.SENT
            EntityNotificationStatus.DELIVERED -> DomainNotificationStatus.DELIVERED
            EntityNotificationStatus.FAILED -> DomainNotificationStatus.FAILED
            EntityNotificationStatus.CANCELLED -> DomainNotificationStatus.CANCELLED
        }
    }
}
