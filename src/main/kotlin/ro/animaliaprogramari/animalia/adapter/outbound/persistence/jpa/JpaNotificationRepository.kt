package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper.NotificationEntityMapper
import ro.animaliaprogramari.animalia.application.port.outbound.NotificationRepository
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.model.notification.*
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

/**
 * JPA adapter implementing the NotificationRepository port
 */
@Repository
class JpaNotificationRepository(
    private val springRepository: SpringNotificationRepository,
    private val mapper: NotificationEntityMapper,
) : NotificationRepository {

    override fun save(notification: Notification): Notification {
        val entity = mapper.toEntity(notification)
        val savedEntity = springRepository.save(entity)
        return mapper.toDomain(savedEntity)
    }

    override fun findById(id: NotificationId): Notification? {
        return springRepository.findById(id.value)
            .map { mapper.toDomain(it) }
            .orElse(null)
    }

    override fun findBySalonId(salonId: SalonId, limit: Int, offset: Int): List<Notification> {
        val pageable = PageRequest.of(offset / limit, limit)
        return springRepository.findBySalonId(salonId.value, pageable)
            .map { mapper.toDomain(it) }
    }

    override fun findByUserId(userId: UserId, limit: Int, offset: Int): List<Notification> {
        val pageable = PageRequest.of(offset / limit, limit)
        return springRepository.findByUserId(userId.value, pageable)
            .map { mapper.toDomain(it) }
    }

    override fun findByAppointmentId(appointmentId: AppointmentId): List<Notification> {
        return springRepository.findByAppointmentId(appointmentId.value)
            .map { mapper.toDomain(it) }
    }

    override fun findByStatus(status: NotificationStatus, limit: Int): List<Notification> {
        val pageable = PageRequest.of(0, limit)
        val entityStatus = mapDomainStatusToEntity(status)
        return springRepository.findByStatus(entityStatus, pageable)
            .map { mapper.toDomain(it) }
    }

    override fun findByTypeAndSalonId(type: SmsNotificationType, salonId: SalonId): List<Notification> {
        val entityType = mapDomainTypeToEntity(type)
        return springRepository.findByTypeAndSalonId(entityType, salonId.value)
            .map { mapper.toDomain(it) }
    }

    override fun countByStatusAndSalonId(status: NotificationStatus, salonId: SalonId): Long {
        val entityStatus = mapDomainStatusToEntity(status)
        return springRepository.countByStatusAndSalonId(entityStatus, salonId.value)
    }

    override fun countByTypeAndSalonId(type: SmsNotificationType, salonId: SalonId): Long {
        val entityType = mapDomainTypeToEntity(type)
        return springRepository.countByTypeAndSalonId(entityType, salonId.value)
    }

    override fun countTodayBySalonId(salonId: SalonId): Long {
        return springRepository.countTodayBySalonId(salonId.value)
    }

    override fun countThisWeekBySalonId(salonId: SalonId): Long {
        val weekStart = LocalDateTime.now().truncatedTo(ChronoUnit.DAYS).minusDays(7)
        return springRepository.countThisWeekBySalonId(salonId.value, weekStart)
    }

    override fun countThisMonthBySalonId(salonId: SalonId): Long {
        val monthStart = LocalDateTime.now().truncatedTo(ChronoUnit.DAYS).minusDays(30)
        return springRepository.countThisMonthBySalonId(salonId.value, monthStart)
    }

    override fun getAverageDeliveryTime(salonId: SalonId): Double? {
        return springRepository.getAverageDeliveryTime(salonId.value)
    }

    override fun findByCreatedAtBetween(startDate: LocalDateTime, endDate: LocalDateTime): List<Notification> {
        return springRepository.findByCreatedAtBetween(startDate, endDate)
            .map { mapper.toDomain(it) }
    }

    override fun deleteOlderThan(cutoffDate: LocalDateTime): Int {
        return springRepository.deleteOlderThan(cutoffDate)
    }

    override fun markAsReadByUserId(userId: UserId, salonId: SalonId): Int {
        return springRepository.markAsReadByUserId(userId.value, salonId.value)
    }

    private fun mapDomainStatusToEntity(domainStatus: NotificationStatus): ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationStatus {
        return when (domainStatus) {
            NotificationStatus.PENDING -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationStatus.PENDING
            NotificationStatus.SENT -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationStatus.SENT
            NotificationStatus.DELIVERED -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationStatus.DELIVERED
            NotificationStatus.FAILED -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationStatus.FAILED
            NotificationStatus.CANCELLED -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationStatus.CANCELLED
        }
    }

    private fun mapDomainTypeToEntity(domainType: SmsNotificationType): ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationType {
        return when (domainType) {
            SmsNotificationType.SMS -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationType.SMS
            SmsNotificationType.PUSH -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationType.PUSH
            SmsNotificationType.EMAIL -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationType.EMAIL
        }
    }
}
