package ro.animaliaprogramari.animalia.domain.service

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.application.port.outbound.AppointmentRepository
import ro.animaliaprogramari.animalia.application.port.outbound.ClientRepository
import ro.animaliaprogramari.animalia.application.port.outbound.PetRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SalonRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SmsSender
import ro.animaliaprogramari.animalia.config.SmsMessageTemplates
import ro.animaliaprogramari.animalia.domain.event.appointment.AppointmentReminderType
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate
import java.time.LocalTime
import java.time.format.DateTimeFormatter

/**
 * Service for creating and sending personalized SMS notifications for appointments.
 * Handles data retrieval, message templating, and SMS delivery with proper error handling.
 */
@Service
class PersonalizedSmsService(
    private val clientRepository: ClientRepository,
    private val salonRepository: SalonRepository,
    private val petRepository: PetRepository,
    private val appointmentRepository: AppointmentRepository,
    private val smsSender: SmsSender,
) {
    private val logger = LoggerFactory.getLogger(PersonalizedSmsService::class.java)

    // Romanian date and time formatters
    private val dateFormatter = DateTimeFormatter.ofPattern("dd.MM.yyyy")
    private val timeFormatter = DateTimeFormatter.ofPattern("HH:mm")

    /**
     * Send appointment scheduled notification
     */
    fun sendAppointmentScheduledNotification(
        clientId: ClientId,
        petId: PetId?,
        appointmentId: AppointmentId?,
        appointmentDate: LocalDate,
        startTime: LocalTime,
    ) {
        val context = buildMessageContext(clientId, petId, appointmentId)
        val message = buildAppointmentScheduledMessage(context, appointmentDate, startTime)
        sendSmsToClient(clientId, message)
    }

    /**
     * Send appointment cancelled notification
     */
    fun sendAppointmentCancelledNotification(
        clientId: ClientId,
        petId: PetId?,
        appointmentId: AppointmentId?,
        appointmentDate: LocalDate,
    ) {
        val context = buildMessageContext(clientId, petId, appointmentId)
        val message = buildAppointmentCancelledMessage(context, appointmentDate)
        sendSmsToClient(clientId, message)
    }

    /**
     * Send appointment rescheduled notification
     */
    fun sendAppointmentRescheduledNotification(
        clientId: ClientId,
        petId: PetId?,
        appointmentId: AppointmentId?,
        oldDate: LocalDate,
        newDate: LocalDate,
        newStartTime: LocalTime,
    ) {
        val context = buildMessageContext(clientId, petId, appointmentId)
        val message = buildAppointmentRescheduledMessage(context, oldDate, newDate, newStartTime)
        sendSmsToClient(clientId, message)
    }

    /**
     * Send appointment deleted notification
     */
    fun sendAppointmentDeletedNotification(
        clientId: ClientId,
        petId: PetId?,
        appointmentId: AppointmentId?,
        appointmentDate: LocalDate,
    ) {
        val context = buildMessageContext(clientId, petId, appointmentId)
        val message = buildAppointmentDeletedMessage(context, appointmentDate)
        sendSmsToClient(clientId, message)
    }

    /**
     * Send appointment reminder notification
     */
    fun sendAppointmentReminderNotification(
        clientId: ClientId,
        petId: PetId?,
        appointmentId: AppointmentId?,
        appointmentDate: LocalDate,
        startTime: LocalTime?,
        reminderType: AppointmentReminderType,
    ) {
        val context = buildMessageContext(clientId, petId, appointmentId)
        val message = buildAppointmentReminderMessage(context, appointmentDate, startTime, reminderType)
        sendSmsToClient(clientId, message)
    }

    /**
     * Build message context by gathering all necessary data
     */
    private fun buildMessageContext(
        clientId: ClientId,
        petId: PetId?,
        appointmentId: AppointmentId?,
    ): MessageContext {
        val client = try {
            clientRepository.findById(clientId)
        } catch (e: Exception) {
            logger.warn("Failed to retrieve client ${clientId.value}: ${e.message}")
            null
        }

        val pet = petId?.let {
            try {
                petRepository.findById(it)
            } catch (e: Exception) {
                logger.warn("Failed to retrieve pet ${it.value}: ${e.message}")
                null
            }
        }

        val appointment = appointmentId?.let {
            try {
                appointmentRepository.findById(it)
            } catch (e: Exception) {
                logger.warn("Failed to retrieve appointment ${it.value}: ${e.message}")
                null
            }
        }

        val salon = appointment?.salonId?.let {
            try {
                salonRepository.findById(it)
            } catch (e: Exception) {
                logger.warn("Failed to retrieve salon ${it.value}: ${e.message}")
                null
            }
        }

        return MessageContext(
            clientName = client?.name,
            petName = pet?.name,
            salonName = salon?.name,
            salonPhone = salon?.phone?.toInternationalFormat(),
            clientPhone = client?.phone
        )
    }

    /**
     * Build appointment scheduled message
     */
    private fun buildAppointmentScheduledMessage(
        context: MessageContext,
        appointmentDate: LocalDate,
        startTime: LocalTime,
    ): String {
        val formattedDate = appointmentDate.format(dateFormatter)
        val formattedTime = startTime.format(timeFormatter)

        val salonPhone = context.salonPhone ?: SmsMessageTemplates.DEFAULT_SALON_PHONE

        return when {
            context.hasAllInfo() -> {
                SmsMessageTemplates.APPOINTMENT_SCHEDULED
                    .replace("{salonName}", context.salonName!!)
                    .replace("{clientName}", context.clientName!!)
                    .replace("{petName}", context.petName!!)
                    .replace("{date}", formattedDate)
                    .replace("{time}", formattedTime)
                    .replace("{salonPhone}", salonPhone)
            }
            context.clientName != null && context.salonName != null -> {
                SmsMessageTemplates.APPOINTMENT_SCHEDULED_NO_PET
                    .replace("{salonName}", context.salonName)
                    .replace("{clientName}", context.clientName)
                    .replace("{date}", formattedDate)
                    .replace("{time}", formattedTime)
                    .replace("{salonPhone}", salonPhone)
            }
            context.petName != null && context.salonName != null -> {
                SmsMessageTemplates.APPOINTMENT_SCHEDULED_NO_CLIENT
                    .replace("{salonName}", context.salonName)
                    .replace("{petName}", context.petName)
                    .replace("{date}", formattedDate)
                    .replace("{time}", formattedTime)
                    .replace("{salonPhone}", salonPhone)
            }
            else -> {
                SmsMessageTemplates.APPOINTMENT_SCHEDULED_MINIMAL
                    .replace("{date}", formattedDate)
                    .replace("{time}", formattedTime)
            }
        }
    }

    /**
     * Build appointment cancelled message
     */
    private fun buildAppointmentCancelledMessage(
        context: MessageContext,
        appointmentDate: LocalDate,
    ): String {
        val formattedDate = appointmentDate.format(dateFormatter)
        val salonName = context.salonName ?: SmsMessageTemplates.DEFAULT_SALON_NAME
        val clientName = context.clientName ?: SmsMessageTemplates.DEFAULT_CLIENT_NAME
        val petName = context.petName ?: SmsMessageTemplates.DEFAULT_PET_REFERENCE
        val salonPhone = context.salonPhone ?: SmsMessageTemplates.DEFAULT_SALON_PHONE

        return SmsMessageTemplates.APPOINTMENT_CANCELLED
            .replace("{salonName}", salonName)
            .replace("{clientName}", clientName)
            .replace("{petName}", petName)
            .replace("{date}", formattedDate)
            .replace("{salonPhone}", salonPhone)
    }

    /**
     * Build appointment rescheduled message
     */
    private fun buildAppointmentRescheduledMessage(
        context: MessageContext,
        oldDate: LocalDate,
        newDate: LocalDate,
        newStartTime: LocalTime,
    ): String {
        val formattedOldDate = oldDate.format(dateFormatter)
        val formattedNewDate = newDate.format(dateFormatter)
        val formattedNewTime = newStartTime.format(timeFormatter)
        val salonName = context.salonName ?: SmsMessageTemplates.DEFAULT_SALON_NAME
        val clientName = context.clientName ?: SmsMessageTemplates.DEFAULT_CLIENT_NAME
        val petName = context.petName ?: SmsMessageTemplates.DEFAULT_PET_REFERENCE
        val salonPhone = context.salonPhone ?: SmsMessageTemplates.DEFAULT_SALON_PHONE

        return SmsMessageTemplates.APPOINTMENT_RESCHEDULED
            .replace("{salonName}", salonName)
            .replace("{clientName}", clientName)
            .replace("{petName}", petName)
            .replace("{oldDate}", formattedOldDate)
            .replace("{newDate}", formattedNewDate)
            .replace("{newTime}", formattedNewTime)
            .replace("{salonPhone}", salonPhone)
    }

    /**
     * Build appointment deleted message
     */
    private fun buildAppointmentDeletedMessage(
        context: MessageContext,
        appointmentDate: LocalDate,
    ): String {
        val formattedDate = appointmentDate.format(dateFormatter)
        val salonName = context.salonName ?: SmsMessageTemplates.DEFAULT_SALON_NAME
        val clientName = context.clientName ?: SmsMessageTemplates.DEFAULT_CLIENT_NAME
        val petName = context.petName ?: SmsMessageTemplates.DEFAULT_PET_REFERENCE
        val salonPhone = context.salonPhone ?: SmsMessageTemplates.DEFAULT_SALON_PHONE

        return SmsMessageTemplates.APPOINTMENT_DELETED
            .replace("{salonName}", salonName)
            .replace("{clientName}", clientName)
            .replace("{petName}", petName)
            .replace("{date}", formattedDate)
            .replace("{salonPhone}", salonPhone)
    }

    /**
     * Build appointment reminder message
     */
    private fun buildAppointmentReminderMessage(
        context: MessageContext,
        appointmentDate: LocalDate,
        startTime: LocalTime?,
        reminderType: AppointmentReminderType,
    ): String {
        val formattedDate = appointmentDate.format(dateFormatter)
        val formattedTime = startTime?.format(timeFormatter) ?: "ora programată"
        val salonName = context.salonName ?: SmsMessageTemplates.DEFAULT_SALON_NAME
        val clientName = context.clientName ?: SmsMessageTemplates.DEFAULT_CLIENT_NAME
        val petName = context.petName ?: SmsMessageTemplates.DEFAULT_PET_REFERENCE
        val salonPhone = context.salonPhone ?: SmsMessageTemplates.DEFAULT_SALON_PHONE

        val template = when (reminderType) {
            AppointmentReminderType.DAY_BEFORE -> SmsMessageTemplates.REMINDER_DAY_BEFORE
            AppointmentReminderType.SIX_HOURS_BEFORE -> SmsMessageTemplates.REMINDER_SIX_HOURS_BEFORE
        }

        return template
            .replace("{salonName}", salonName)
            .replace("{clientName}", clientName)
            .replace("{petName}", petName)
            .replace("{date}", formattedDate)
            .replace("{time}", formattedTime)
            .replace("{salonPhone}", salonPhone)
    }

    /**
     * Send SMS to client with proper error handling
     */
    private fun sendSmsToClient(clientId: ClientId, message: String) {
        if (true) {
            logger.info("SMS content: $message")
            return
        }

        try {
            val client = clientRepository.findById(clientId)
            val phone = client?.phone?.toInternationalFormat()
            if (phone != null) {
                smsSender.sendSms(phone, message)
                logger.debug("SMS sent successfully to client ${clientId.value}")
            } else {
                logger.debug("No phone number found for client ${clientId.value}")
            }
        } catch (ex: Exception) {
            logger.error("Failed to send SMS to client ${clientId.value}", ex)
        }
    }

    /**
     * Data class to hold message context information
     */
    private data class MessageContext(
        val clientName: String?,
        val petName: String?,
        val salonName: String?,
        val salonPhone: String?,
        val clientPhone: PhoneNumber?,
    ) {
        fun hasAllInfo(): Boolean = clientName != null && petName != null && salonName != null
    }
}
