package ro.animaliaprogramari.animalia.application.usecase

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.adapter.inbound.event.FirebasePushService
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.notification.*
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.port.inbound.NotificationManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.*
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.model.notification.*
import java.time.LocalDateTime
import kotlin.random.Random

/**
 * Implementation of notification management use case
 * Contains the business logic for notification operations
 */
@Service
class NotificationManagementUseCaseImpl(
    private val notificationRepository: NotificationRepository,
    private val fcmTokenRepository: FcmTokenRepository,
    private val salonRepository: SalonRepository,
    private val userRepository: UserRepository,
    private val firebasePushService: FirebasePushService,
) : NotificationManagementUseCase {
    private val logger = LoggerFactory.getLogger(NotificationManagementUseCaseImpl::class.java)

    override fun generateTestData(command: GenerateTestNotificationDataCommand): GenerateTestDataResponse {
        logger.info("Generating ${command.count} test notifications for salon: ${command.salonId.value}")

        // Verify salon exists
        salonRepository.findById(command.salonId)
            ?: throw EntityNotFoundException("Salon not found: ${command.salonId.value}")

        val notificationIds = mutableListOf<String>()
        var fcmTokensCreated = 0

        // Generate test FCM tokens if none exist
        val existingTokens = fcmTokenRepository.findActiveBySalonId(command.salonId)
        if (existingTokens.isEmpty()) {
            repeat(3) { index ->
                val testToken = FcmToken.create(
                    userId = command.userId,
                    salonId = command.salonId,
                    token = "test_token_${System.currentTimeMillis()}_$index",
                    deviceId = "test_device_$index",
                    deviceType = DeviceType.MOBILE
                )
                fcmTokenRepository.save(testToken)
                fcmTokensCreated++
            }
        }

        // Generate test notifications
        repeat(command.count) { index ->
            val notification = when (index % 3) {
                0 -> Notification.createPush(
                    recipient = command.userId,
                    title = "Test Push Notification #${index + 1}",
                    body = "This is a test push notification generated at ${LocalDateTime.now()}",
                    appointmentId = null,
                    salonId = command.salonId
                )
                1 -> Notification.createSms(
                    recipient = PhoneNumber.of("+40712345678"),
                    message = "Test SMS notification #${index + 1} - ${LocalDateTime.now()}",
                    appointmentId = null,
                    salonId = command.salonId
                )
                else -> Notification.createPush(
                    recipient = command.userId,
                    title = "Appointment Reminder",
                    body = "You have an appointment tomorrow at 10:00 AM",
                    appointmentId = null,
                    salonId = command.salonId
                ).markAsSent()
            }

            val savedNotification = notificationRepository.save(notification)
            notificationIds.add(savedNotification.id.value)
        }

        logger.info("Generated ${command.count} test notifications and $fcmTokensCreated FCM tokens")

        return GenerateTestDataResponse(
            notificationsCreated = command.count,
            fcmTokensCreated = fcmTokensCreated,
            message = "Successfully generated ${command.count} test notifications and $fcmTokensCreated FCM tokens",
            notificationIds = notificationIds
        )
    }

    override fun getNotificationStats(query: GetNotificationStatsQuery): NotificationStatsResponse {
        logger.info("Getting notification stats for salon: ${query.salonId.value}")

        // Verify salon exists
        salonRepository.findById(query.salonId)
            ?: throw EntityNotFoundException("Salon not found: ${query.salonId.value}")

        val totalSent = notificationRepository.countByStatusAndSalonId(NotificationStatus.SENT, query.salonId)
        val totalDelivered = notificationRepository.countByStatusAndSalonId(NotificationStatus.DELIVERED, query.salonId)
        val totalFailed = notificationRepository.countByStatusAndSalonId(NotificationStatus.FAILED, query.salonId)
        val totalPending = notificationRepository.countByStatusAndSalonId(NotificationStatus.PENDING, query.salonId)

        val smsNotifications = notificationRepository.countByTypeAndSalonId(SmsNotificationType.SMS, query.salonId)
        val pushNotifications = notificationRepository.countByTypeAndSalonId(SmsNotificationType.PUSH, query.salonId)

        val activeFcmTokens = fcmTokenRepository.countActiveBySalonId(query.salonId)

        val notificationsToday = notificationRepository.countTodayBySalonId(query.salonId)
        val notificationsThisWeek = notificationRepository.countThisWeekBySalonId(query.salonId)
        val notificationsThisMonth = notificationRepository.countThisMonthBySalonId(query.salonId)

        val averageDeliveryTime = notificationRepository.getAverageDeliveryTime(query.salonId)

        val totalAttempted = totalSent + totalDelivered + totalFailed
        val successRate = if (totalAttempted > 0) {
            ((totalSent + totalDelivered).toDouble() / totalAttempted.toDouble()) * 100.0
        } else {
            0.0
        }

        return NotificationStatsResponse(
            totalSent = totalSent,
            totalDelivered = totalDelivered,
            totalFailed = totalFailed,
            totalPending = totalPending,
            smsNotifications = smsNotifications,
            pushNotifications = pushNotifications,
            activeFcmTokens = activeFcmTokens,
            notificationsToday = notificationsToday,
            notificationsThisWeek = notificationsThisWeek,
            notificationsThisMonth = notificationsThisMonth,
            averageDeliveryTime = averageDeliveryTime,
            successRate = successRate
        )
    }

    override fun markAllAsRead(command: BulkMarkNotificationsAsReadCommand): BulkMarkAsReadResponse {
        logger.info("Marking all notifications as read for user: ${command.userId.value}")

        val markedCount = notificationRepository.markAsReadByUserId(command.userId, command.salonId)

        return BulkMarkAsReadResponse(
            notificationsMarked = markedCount,
            message = "Successfully marked $markedCount notifications as read"
        )
    }

    override fun sendTestNotification(command: SendTestNotificationCommand): SendTestNotificationResponse {
        logger.info("Sending test notification for user: ${command.userId.value}")

        return try {
            // Create test notification
            val notification = Notification.createPush(
                recipient = command.userId,
                title = command.title,
                body = command.message,
                appointmentId = null,
                salonId = command.salonId
            )

            val savedNotification = notificationRepository.save(notification)

            // Send via Firebase
            firebasePushService.sendToStaff(
                salonId = command.salonId.value,
                title = command.title,
                body = command.message
            )

            // Mark as sent
            val sentNotification = savedNotification.markAsSent()
            notificationRepository.save(sentNotification)

            // Count devices notified
            val devicesNotified = fcmTokenRepository.countActiveBySalonId(command.salonId).toInt()

            SendTestNotificationResponse(
                success = true,
                notificationId = savedNotification.id.value,
                devicesNotified = devicesNotified,
                message = "Test notification sent successfully to $devicesNotified devices"
            )
        } catch (e: Exception) {
            logger.error("Failed to send test notification", e)
            SendTestNotificationResponse(
                success = false,
                notificationId = null,
                devicesNotified = 0,
                message = "Failed to send test notification: ${e.message}"
            )
        }
    }

    override fun registerFcmToken(command: RegisterFcmTokenCommand): FcmTokenResponse {
        logger.info("Registering FCM token for user: ${command.userId.value}")

        // Verify user and salon exist
        userRepository.findById(command.userId)
            ?: throw EntityNotFoundException("User not found: ${command.userId.value}")
        salonRepository.findById(command.salonId)
            ?: throw EntityNotFoundException("Salon not found: ${command.salonId.value}")

        // Check if token already exists
        val existingToken = fcmTokenRepository.findByToken(command.token)
        if (existingToken != null) {
            // Reactivate if it was deactivated
            val reactivatedToken = existingToken.reactivate()
            val savedToken = fcmTokenRepository.save(reactivatedToken)
            return FcmTokenResponse.from(savedToken)
        }

        // Deactivate other tokens for this user/salon combination
        fcmTokenRepository.deactivateOtherTokens(command.userId, command.salonId, command.token)

        // Create new token
        val deviceType = try {
            DeviceType.valueOf(command.deviceType.uppercase())
        } catch (e: IllegalArgumentException) {
            DeviceType.MOBILE
        }

        val fcmToken = FcmToken.create(
            userId = command.userId,
            salonId = command.salonId,
            token = command.token,
            deviceId = command.deviceId,
            deviceType = deviceType
        )

        val savedToken = fcmTokenRepository.save(fcmToken)
        logger.info("FCM token registered successfully for user: ${command.userId.value}")

        return FcmTokenResponse.from(savedToken)
    }

    override fun updateFcmToken(command: UpdateFcmTokenCommand): FcmTokenResponse {
        logger.info("Updating FCM token for user: ${command.userId.value}")

        val existingToken = fcmTokenRepository.findByToken(command.oldToken)
            ?: throw EntityNotFoundException("FCM token not found: ${command.oldToken}")

        val updatedToken = existingToken.updateToken(command.newToken)
        val savedToken = fcmTokenRepository.save(updatedToken)

        logger.info("FCM token updated successfully for user: ${command.userId.value}")
        return FcmTokenResponse.from(savedToken)
    }

    override fun deactivateFcmToken(command: DeactivateFcmTokenCommand): Boolean {
        logger.info("Deactivating FCM token for user: ${command.userId.value}")

        val existingToken = fcmTokenRepository.findByToken(command.token)
            ?: return false

        val deactivatedToken = existingToken.deactivate()
        fcmTokenRepository.save(deactivatedToken)

        logger.info("FCM token deactivated successfully for user: ${command.userId.value}")
        return true
    }

    override fun getUserNotifications(query: GetUserNotificationsQuery): PaginatedNotificationsResponse {
        logger.info("Getting notifications for user: ${query.userId.value}, page: ${query.page}, pageSize: ${query.pageSize}")

        // Parse notification type if provided
        val notificationType = query.type?.let { typeStr ->
            try {
                SmsNotificationType.valueOf(typeStr.uppercase())
            } catch (e: IllegalArgumentException) {
                null
            }
        }

        // Get notifications with filtering
        val notifications = if (notificationType != null) {
            notificationRepository.findByUserIdAndType(query.userId, query.salonId, notificationType, query.limit, query.offset)
        } else {
            notificationRepository.findByUserId(query.userId, query.limit, query.offset)
        }

        // Get counts
        val totalCount = notificationRepository.countByUserId(query.userId, query.salonId)
        val unreadCount = notificationRepository.countUnreadByUserId(query.userId, query.salonId)

        // Calculate if there are more pages
        val hasMore = (query.offset + query.pageSize) < totalCount

        return PaginatedNotificationsResponse(
            notifications = notifications.map { NotificationResponse.from(it) },
            totalCount = totalCount,
            unreadCount = unreadCount,
            page = query.page,
            pageSize = query.pageSize,
            hasMore = hasMore
        )
    }

    override fun getUserFcmTokens(query: GetUserFcmTokensQuery): List<FcmTokenResponse> {
        logger.info("Getting FCM tokens for user: ${query.userId.value}")

        val tokens = if (query.activeOnly) {
            fcmTokenRepository.findActiveByUserIdAndSalonId(query.userId, query.salonId)
        } else {
            fcmTokenRepository.findActiveByUserId(query.userId)
        }

        return tokens.map { FcmTokenResponse.from(it) }
    }
}
