package ro.animaliaprogramari.animalia.application.query

import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDateTime

/**
 * Query to get notification statistics
 */
data class GetNotificationStatsQuery(
    val userId: UserId,
    val salonId: SalonId,
    val startDate: LocalDateTime? = null,
    val endDate: LocalDateTime? = null
)

/**
 * Query to get notifications for a user
 */
data class GetUserNotificationsQuery(
    val userId: UserId,
    val salonId: SalonId,
    val limit: Int = 50,
    val offset: Int = 0,
    val unreadOnly: Boolean = false
) {
    init {
        require(limit > 0 && limit <= 100) { "Limit must be between 1 and 100" }
        require(offset >= 0) { "Offset must be non-negative" }
    }
}

/**
 * Query to get FCM tokens for a user
 */
data class GetUserFcmTokensQuery(
    val userId: UserId,
    val salonId: SalonId,
    val activeOnly: Boolean = true
)

/**
 * Query to get FCM tokens for a salon
 */
data class GetSalonFcmTokensQuery(
    val salonId: SalonId,
    val activeOnly: Boolean = true
)

/**
 * Query to find FCM token by token value
 */
data class FindFcmTokenQuery(
    val token: String,
    val activeOnly: Boolean = true
) {
    init {
        require(token.isNotBlank()) { "Token cannot be blank" }
    }
}
