package ro.animaliaprogramari.animalia.application.port.inbound

import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.notification.*
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.query.*

/**
 * Inbound port for notification management operations
 * This interface defines the business operations available for notification management
 */
interface NotificationManagementUseCase {
    /**
     * Generate test notification data for development and testing
     */
    fun generateTestData(command: GenerateTestNotificationDataCommand): GenerateTestDataResponse

    /**
     * Get comprehensive notification statistics
     */
    fun getNotificationStats(query: GetNotificationStatsQuery): NotificationStatsResponse

    /**
     * Mark all notifications as read for a user (bulk operation)
     */
    fun markAllAsRead(command: BulkMarkNotificationsAsReadCommand): BulkMarkAsReadResponse

    /**
     * Send a test notification to verify the system is working
     */
    fun sendTestNotification(command: SendTestNotificationCommand): SendTestNotificationResponse

    /**
     * Register FCM token for push notifications
     */
    fun registerFcmToken(command: RegisterFcmTokenCommand): FcmTokenResponse

    /**
     * Update existing FCM token
     */
    fun updateFcmToken(command: UpdateFcmTokenCommand): FcmTokenResponse

    /**
     * Deactivate FCM token
     */
    fun deactivateFcmToken(command: DeactivateFcmTokenCommand): Boolean

    /**
     * Get user notifications with pagination
     */
    fun getUserNotifications(query: GetUserNotificationsQuery): PaginatedNotificationsResponse

    /**
     * Get user FCM tokens
     */
    fun getUserFcmTokens(query: GetUserFcmTokensQuery): List<FcmTokenResponse>
}
