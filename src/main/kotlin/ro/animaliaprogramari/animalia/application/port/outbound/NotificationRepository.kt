package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.model.notification.*
import java.time.LocalDateTime

/**
 * Outbound port for notification persistence
 * This interface defines the contract for notification data access
 */
interface NotificationRepository {
    /**
     * Save notification
     */
    fun save(notification: Notification): Notification

    /**
     * Find notification by ID
     */
    fun findById(id: NotificationId): Notification?

    /**
     * Find notifications by salon ID
     */
    fun findBySalonId(salonId: SalonId, limit: Int = 50, offset: Int = 0): List<Notification>

    /**
     * Find notifications by user ID (for push notifications)
     */
    fun findByUserId(userId: UserId, limit: Int = 50, offset: Int = 0): List<Notification>

    /**
     * Find notifications by appointment ID
     */
    fun findByAppointmentId(appointmentId: AppointmentId): List<Notification>

    /**
     * Find notifications by status
     */
    fun findByStatus(status: NotificationStatus, limit: Int = 50): List<Notification>

    /**
     * Find notifications by type and salon
     */
    fun findByTypeAndSalonId(type: SmsNotificationType, salonId: SalonId): List<Notification>

    /**
     * Count notifications by status and salon
     */
    fun countByStatusAndSalonId(status: NotificationStatus, salonId: SalonId): Long

    /**
     * Count notifications by type and salon
     */
    fun countByTypeAndSalonId(type: SmsNotificationType, salonId: SalonId): Long

    /**
     * Count notifications sent today for a salon
     */
    fun countTodayBySalonId(salonId: SalonId): Long

    /**
     * Count notifications sent this week for a salon
     */
    fun countThisWeekBySalonId(salonId: SalonId): Long

    /**
     * Count notifications sent this month for a salon
     */
    fun countThisMonthBySalonId(salonId: SalonId): Long

    /**
     * Get average delivery time for successful notifications
     */
    fun getAverageDeliveryTime(salonId: SalonId): Double?

    /**
     * Find notifications created between dates
     */
    fun findByCreatedAtBetween(startDate: LocalDateTime, endDate: LocalDateTime): List<Notification>

    /**
     * Delete old notifications (cleanup)
     */
    fun deleteOlderThan(cutoffDate: LocalDateTime): Int

    /**
     * Mark notifications as read for a user
     */
    fun markAsReadByUserId(userId: UserId, salonId: SalonId): Int
}
