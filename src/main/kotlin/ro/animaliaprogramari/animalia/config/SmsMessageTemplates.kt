package ro.animaliaprogramari.animalia.config

/**
 * Configuration class containing all SMS message templates for appointment notifications.
 * All messages are in Romanian and follow a professional, warm, and personalized tone.
 */
object SmsMessageTemplates {

    /**
     * Template for appointment scheduled/created notification
     * Variables: {salonName}, {clientName}, {petName}, {date}, {time}, {salonPhone}
     */
    const val APPOINTMENT_SCHEDULED = "De la {salonName} - Bună, {clientName}! Programarea pentru {petName} este confirmată pe {date} la {time}. Te așteptăm cu drag! Pentru întrebări: {salonPhone} 🐾"

    /**
     * Template for appointment cancelled notification
     * Variables: {salonName}, {clientName}, {petName}, {date}, {salonPhone}
     */
    const val APPOINTMENT_CANCELLED = "De la {salonName} - Bună, {clientName}! Programarea pentru {petName} din {date} a fost anulată. Pentru reprogramare: {salonPhone}. Mulțumim pentru înțelegere! 🐾"

    /**
     * Template for appointment rescheduled notification
     * Variables: {salonName}, {clientName}, {petName}, {oldDate}, {newDate}, {newTime}, {salonPhone}
     */
    const val APPOINTMENT_RESCHEDULED = "De la {salonName} - Bună, {clientName}! Programarea pentru {petName} a fost reprogramată de pe {oldDate} pentru {newDate} la {newTime}. Te așteptăm cu drag! Pentru întrebări: {salonPhone} 🐾"

    /**
     * Template for appointment deleted notification
     * Variables: {salonName}, {clientName}, {petName}, {date}, {salonPhone}
     */
    const val APPOINTMENT_DELETED = "De la {salonName} - Bună, {clientName}! Programarea pentru {petName} din {date} a fost ștearsă din sistem. Pentru întrebări: {salonPhone}. Mulțumim! 🐾"

    /**
     * Template for day before reminder
     * Variables: {salonName}, {clientName}, {petName}, {date}, {time}, {salonPhone}
     */
    const val REMINDER_DAY_BEFORE = "De la {salonName} - Bună, {clientName}! Îți amintim că mâine ({date}) la {time} îl așteptăm pe {petName} la programare. Ne bucurăm să vă vedem! Pentru întrebări: {salonPhone} 🐾"

    /**
     * Template for 6 hours before reminder
     * Variables: {salonName}, {clientName}, {petName}, {date}, {time}, {salonPhone}
     */
    const val REMINDER_SIX_HOURS_BEFORE = "De la {salonName} - Bună, {clientName}! În câteva ore ({time}) îl așteptăm pe {petName} la programare. Vă mulțumim că ați ales serviciile noastre! Pentru întrebări: {salonPhone} 🐾"

    /**
     * Fallback template when pet name is not available
     * Variables: {salonName}, {clientName}, {date}, {time}, {salonPhone}
     */
    const val APPOINTMENT_SCHEDULED_NO_PET = "De la {salonName} - Bună, {clientName}! Programarea dumneavoastră este confirmată pe {date} la {time}. Te așteptăm cu drag! Pentru întrebări: {salonPhone} 🐾"

    /**
     * Fallback template when client name is not available
     * Variables: {salonName}, {petName}, {date}, {time}, {salonPhone}
     */
    const val APPOINTMENT_SCHEDULED_NO_CLIENT = "De la {salonName} - Programarea pentru {petName} este confirmată pe {date} la {time}. Vă așteptăm cu drag! Pentru întrebări: {salonPhone} 🐾"

    /**
     * Minimal fallback template when only basic info is available
     * Variables: {date}, {time}
     */
    const val APPOINTMENT_SCHEDULED_MINIMAL = "Programarea dumneavoastră este confirmată pe {date} la {time}. Vă mulțumim! 🐾"

    /**
     * Default salon name when salon information is not available
     */
    const val DEFAULT_SALON_NAME = "Salonul nostru"

    /**
     * Default client name when client information is not available
     */
    const val DEFAULT_CLIENT_NAME = "Stimate client"

    /**
     * Default pet reference when pet information is not available
     */
    const val DEFAULT_PET_REFERENCE = "animalul dumneavoastră"

    /**
     * Default salon phone when salon phone is not available
     */
    const val DEFAULT_SALON_PHONE = "salonul nostru"
}
